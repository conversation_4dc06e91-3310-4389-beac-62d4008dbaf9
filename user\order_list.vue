```html
<template>
	<view class="page">
		<view class="header">
			<view class="header_item" v-for="(item, index) in list" :key="index" @click="handleHeader(item)">
				<view :style="currentIndex === item.value ? 'color:#2E80FE;' : ''">{{ item.name }}</view>
				<view class="blue" :style="currentIndex === item.value ? '' : 'background-color:#fff;'"></view>
			</view>
		</view>

		<u-empty mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" v-if="orderList.length === 0">
		</u-empty>

		<!-- 调试信息 -->
		<view v-if="orderList.length === 0" style="text-align: center; padding: 20rpx; color: #999;">
			当前订单数量: {{orderList.length}}
		</view>

		<view @click="dingyue()" class="main">
			<view v-for="(item, index) in orderList" :key="index">
				<view class="main_item" v-if="item.payType >= -1"
					@click="item.type !== 5 ? goUrl(`/user/order_details?id=${item.id}`) : null">
					<view class="head">
						<view class="no">单号：{{ item.orderCode }}</view>
						<view class="type">{{ item.payType === -1 ? '已取消' : pay_typeArr[parseInt(item.payType)] }}
						</view>
					</view>
					<view class="mid">
						<view class="lef">
							<image :src="item.goodsCover" mode=""></image>
							<text>{{ item.goodsName }}</text>
							<text style="color:#F60100 ;" v-if="item.type===5">活动订单</text>
						</view>
						<view class="righ"
							v-if="item.payType === 0 || (item.payType === 1 && parseInt(item.payType) >= 1)">
							<view>￥{{ item.payPrice }}</view>
							<view>x{{ item.num ? item.num : 1 }}</view>
						</view>
					</view>
					<view class="bot">
						<text>{{ item.createTime }}</text>
						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) !== 5"
							@click.stop="gozhifu(item)">
							去支付
						</view>

						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) === 5"
							@click.stop="goUrl(`/user/huodongCashier?id=${item.id}&price=${item.payPrice}&type=${item.payType}&goodsId=${item.goodsId}`)">
							去支付
						</view>
						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) === 5"
							@click.stop="huodongquxiaos(item)">
							取消订单
						</view>
						<view class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.type) !== 5"
							@click.stop="confirmorder(item)">
							确认完成
						</view>
						<view class="qrwc" v-if="item.payType >= 1 && item.payType < 7 && item.refundStatus === 0"
							@click.stop="applyT(item)">
							申请退款
						</view>
						<view class="qrwc"
							v-if="item.payType >= 1 && item.payType < 7 && item.refundStatus !== 0"
							@click.stop="viewRefundDetails(item)">
							查看退款详情
						</view>
						<view class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5"
							@click.stop="huodongwanchengclick(item)">
							确认完成
						</view>
						<view style="color: #999999; background-color: #f0f0f0;" class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5"
							@click.stop="huodongclick()">
							待上门
						</view>
						<view class="qpl" v-if="parseInt(item.payType) === 7 && item.isComment === 0&& item.type === 5"
							@click.stop="gohuodongevaluate(item)">去评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 1 && item.type === 5">
							已评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 0 && item.type !== 5"
							@click.stop="goevaluate(item)">去评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 1 && item.type !== 5">
							已评价
						</view>
					</view>
					
					<!-- 子订单（差价申请列表） -->
					<view v-if="item.orderDiffPrices && item.orderDiffPrices.length > 0" class="sub_orders">
						<view class="sub_title">差价申请记录</view>
						<view class="sub_item" v-for="(diffItem, diffIndex) in item.orderDiffPrices" :key="diffItem.id" @click.stop="">
							<view class="sub_head">
								<view class="sub_no">差价单号：{{ diffItem.diffCode }}</view>
								<view class="sub_status">{{ getDiffStatusText(diffItem.status) }}</view>
							</view>
							<view class="sub_content">
								<view class="sub_info">
									<view class="sub_amount">差价金额：￥{{ diffItem.diffAmount }}</view>
									<view class="sub_reason">原因：{{ diffItem.reasonDetail }}</view>
									<view class="sub_time">申请时间：{{ diffItem.createdTime }}</view>
								</view>
								<view class="sub_actions">
									<view class="sub_qzf" v-if="diffItem.status === 0" @click.stop="confirmDiffPrice(diffItem)">
										确认差价
									</view>
									<view class="sub_qzf sub_reject" v-if="diffItem.status === 0" @click.stop="rejectDiffPrice(diffItem)">
										拒绝差价
									</view>
									<view class="sub_qzf" v-if="diffItem.status === 1" @click.stop="payDiffPrice(diffItem)">
										去支付
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="main_item_already" v-else @click="goChoose(item)">
					<view style="font-size: 32rpx;font-weight: 500;" class="title">
						{{ item.quotedPriceVos.length === 0 ? '等待师傅报价' : '等待您选择师傅' }}
					</view>
					<view class="ok" v-if="item.quotedPriceVos.length > 0">已有师傅报价</view>
					<view class="no">单号：{{ item.orderCode }}</view>
					<view class="mid">
						<view class="lef">
							<image :src="item.goodsCover" mode=""></image>
							<text>{{ item.goodsName }}</text>

						</view>
						<text style="">数量:{{ item.num }}</text>
					</view>
					<view class="bot">
						<text>{{ item.createTime }}</text>
					</view>
					<view class="shifu">
						<scroll-view scroll-x="true">
							<view class="shifu_item" v-for="(shfItem, shfIndex) in item.quotedPriceVos" :key="shfIndex">
								<image :src="shfItem.selfImg ? shfItem.selfImg : '/static/mine/default_user.png'"
									mode="aspectFit"></image>
								<text>￥{{ (shfItem.price * (1 + jiaNum / 100)).toFixed(2) }}</text>
							</view>
						</scroll-view>
					</view>
					<view v-if="item.quotedPriceVos.length > 0"
						style="display: flex; justify-content: center; align-items: center; margin-top: 20rpx;">
						<view class="qxdd" @click.stop="cancelorder(item)">取消订单</view>
						<view style="margin-left: 20%;" class="tips" vif="item.quotedPriceVos.length > 0">
							{{ item.quotedPriceVos.length }}位师傅已报价
						</view>
						<view class="qxdd" style="margin-left: 20rpx;">
							选择师傅
						</view>
					</view>
					<view v-if="item.payType===-3&& parseInt(item.type) !== 5" class="qxdd"
						@click.stop="cancelorder(item)">取消订单</view>
				</view>
			</view>
		</view>

		<u-modal :show="showCancel" title="取消订单" content="确认要取消该订单吗" showCancelButton @cancel="showCancel = false"
			@confirm="confirmCancel"></u-modal>
		<u-modal :show="showConfirm" title="完成订单" content="确认要完成该订单吗" showCancelButton @cancel="showConfirm = false"
			@confirm="confirmconfirm"></u-modal>

		<u-modal :show="showPaymentModal" title="提示" showCancelButton confirm-text="去支付"
			@cancel="showPaymentModal = false" @confirm="confirmPayment">

			<view class="modal-content-red">
				{{ paymentRemind }}
			</view>

		</u-modal>

		<u-modal :show="showRefundModal" title="提示" showCancelButton @cancel="showRefundModal = false"
			@confirm="confirmRefund">
			<view class="modal-content-red">
				{{ reminddata }}
			</view>
		</u-modal>

		<u-modal :show="showRefundDetailsModal" title="退款详情" @cancel="showRefundDetailsModal = false"
			@confirm="showRefundDetailsModal = false" confirm-text="关闭">
			<view class="modal-content-details">
				<view class="detail-item">
					<text class="label">退款单号:</text>
					<text class="value">{{ refundDetails.orderCode }}</text>
				</view>
				<view class="detail-item">
					<text class="label">申请时间:</text>
					<text class="value">{{ refundDetails.createTime }}</text>
				</view>
				<view class="detail-item">
					<text class="label">状态:</text>
					<text class="value">{{ refundStatusText(refundDetails.status) }}</text>
				</view>
				<view class="detail-item" v-if="refundDetails.refundText">
					<text class="label">驳回备注:</text>
					<text class="value">{{ refundDetails.refundText }}</text>
				</view>
				<view class="detail-item" v-if="refundDetails.refundTime">
					<text class="label">审核时间:</text>
					<text class="value">{{ refundDetails.refundTime }}</text>
				</view>
				<view class="detail-item" v-if="refundDetails.outRefundNo">
					<text class="label">退款回执单号:</text>
					<text class="value">{{ refundDetails.outRefundNo }}</text>
				</view>
			</view>
		</u-modal>

		<view style="display: flex; justify-content: center;" v-if="orderList.length >= 10">
			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: 'loadmore',
				showConfirm: false,
				showCancel: false,
				showPaymentModal: false,
				showRefundModal: false,
				showRefundDetailsModal: false,
				currentItem: null,
				refundDetails: {}, // New data property for refund details
				jiaNum: 0,
				reminddata: '若你选择线下交易，无平台监管遭遇诈骗或者纠纷需由您自行承担损失！',
				paymentRemind: '无平台担保的支付可能遭遇“假维修”“小病大修”等套路（据消协数，40%的线下维修投诉涉及虚报故障）',
				huodonglist: [],
				isFromTiaozhuan: false,
				isFromCartPlay: false,
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				list: [{
						name: '全部',
						value: 0
					},
					{
						name: '报价列表',
						value: -2
					},
					{
						name: '待支付',
						value: 1
					},
					{
						name: '待服务',
						value: 5
					},
					{
						name: '服务中',
						value: 6
					},
					{
						name: '已完成',
						value: 7
					}
				],
				currentIndex: 0,
				page: 1,
				orderList: [],
				pay_typeArr: ['', '待支付', '报价列表', '已接单', '上门中', '待服务', '服务中', '已完成'],
				id: '',
				isLoading: false
			};
		},
		onPullDownRefresh() {
			this.page = 1;
			this.orderList = [];
			this.status = 'loadmore';
			this.getList(this.currentIndex).then(() => {
				uni.stopPullDownRefresh();
			}).catch(() => {
				uni.stopPullDownRefresh();
			});
		},
		onReachBottom() {
			if (this.status === 'nomore' || this.isLoading) return;
			this.isLoading = true;
			this.status = 'loading';
			this.page++;
			setTimeout(() => {
				this.$api.service.userOrder({
					payType: this.currentIndex,
					pageNum: this.page,
					pageSize: 10
				}).then(ress => {
					let res = ress.data
					const list = Array.isArray(res.list) ? res.list : [];
					const normalizedList = list.map(item => ({
						...item,
						payType: parseInt(item.payType)
					}));
					this.orderList = [...this.orderList, ...normalizedList];
					this.status = list.length < 10 ? 'nomore' : 'loadmore';
					this.isLoading = false;
				}).catch(err => {
					this.status = 'nomore';
					this.isLoading = false;
					this.page--;
					console.error('Error loading more:', err);
				});
			}, 1500);
		},
		methods: {
			// 获取差价申请状态文本
			getDiffStatusText(status) {
				const statusMap = {
					'-1': '已取消',
					0: '待确认',
					1: '已确认待支付', 
					2: '已支付',
					3: '已拒绝'
				};
				return statusMap[status] || '未知状态';
			},
			
			// 去支付差价
			payDiffPrice(diffItem) {
				this.$api.service.payDiffPrice({
					id: diffItem.id,
					type: 1
				}).then(res => {
					if (res.code === "200") {
						
						console.log(res)
						let obj = res.data
						let packageStr = "prepay_id=" + obj.prepayId;
						console.log(String(packageStr))
						console.log(obj.nonceStr)
						console.log(packageStr)
						console.log(obj.nonceStr)
						console.log(String(obj.timestamp))
						console.log(obj.sign)
						const paymentParams = {
							timeStamp: String(obj.timestamp), // 一定要是 string 
							nonceStr: obj.nonceStr,
							package: "prepay_id=" + obj.prepayId,
							signType: 'MD5',
							paySign: obj.sign
						};
						console.log(JSON.stringify(paymentParams));
						uni.requestPayment({
							"provider": 'wxpay',
							timeStamp: String(obj.timestamp),
							nonceStr: obj.nonceStr,
							package: "prepay_id=" + obj.prepayId,
							partnerid: obj.partnerId,
							signType: "MD5",
							paySign: obj.sign,
							appId: obj.appId,
							success: (res1) => {
								// 支付成功回调
								console.log('支付成功', res1);
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								this.page = 1;
								this.orderList = [];
								this.getList(this.currentIndex);
							},
							fail: (err) => {
								// 支付失败回调
								console.error('requestPayment fail object:', err);
								console.error('requestPayment fail JSON:', JSON.stringify(err));
								if (err.errMsg.includes('fail cancel')) {
									wx.showToast({
										title: '您已取消支付',
										icon: 'none'
									});
								} else {
									wx.showToast({
										title: '支付失败，请稍后重试',
										icon: 'none'
									});
								}
								console.error('支付失败', err);
								uni.showToast({
									title: '支付失败请检查网络',
									icon: 'error'
								})
							},
						})
						
			
						
						
					} else {
						uni.showToast({
							title: res.msg || '支付失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
					console.error('Error in payDiffPrice:', err);
				});
			},
			
			// 确认差价
			confirmDiffPrice(diffItem) {
				uni.showModal({
					title: '确认差价',
					content: `确定要同意差价金额 ￥${diffItem.diffAmount} 吗？`,
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.updateDiffStatus(diffItem, 1);
						}
					}
				});
			},
			
			// 拒绝差价
			rejectDiffPrice(diffItem) {
				uni.showModal({
					title: '拒绝差价',
					content: '请输入拒绝原因',
					editable: true,
					placeholderText: '请输入拒绝原因',
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							const rejectText = res.content || '';
							if (!rejectText.trim()) {
								uni.showToast({
									title: '请输入拒绝原因',
									icon: 'none'
								});
								return;
							}
							this.updateDiffStatus(diffItem, 2, rejectText);
						}
					}
				});
			},
			
			// 更新差价状态
			updateDiffStatus(diffItem, status, text = '') {
				const params = {
					id: diffItem.id,
					status: status
				};
				if (status === 2 && text) {
					params.text = text;
				}
				
				this.$api.service.updateDiffStatus(params).then(res => {
					if (res.code === "200") {
						uni.showToast({
							title: status === 1 ? '已同意' : '已拒绝',
							icon: 'success'
						});
						this.page = 1;
						this.orderList = [];
						this.getList(this.currentIndex);
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
					console.error('Error in updateDiffStatus:', err);
				});
			},
			
			
			
			
			
			huodongclick() {
				uni.showToast({
					icon: 'none',
					title: '耐心等待师傅上门，有问题联系客服'
				});
			},
			gohuodongevaluate(item) {
				uni.navigateTo({
					url: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}&huodong=${1}`
				});
			},
			gozhifu(item) {
				this.currentItem = item;
				this.showPaymentModal = true;
			},
			confirmPayment() {
				this.showPaymentModal = false;
				if (this.currentItem) {
					uni.navigateTo({
						url: `/user/Cashier?id=${this.currentItem.id}&price=${this.currentItem.payPrice}&type=${this.currentItem.payType}&goodsId=${this.currentItem.goodsId}`
					});
				}
			},
			huodongwanchengclick(item) {
				uni.showModal({
					title: '确认完成',
					content: '师傅是否已完成订单？',
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.$api.service.huodongqueding({
								id: item.id
							}).then(res => {
								if (res.code === "200") {
									uni.showToast({
										icon: 'success',
										title: '订单完成'
									});
									this.page = 1;
									this.orderList = [];
									this.getList(this.currentIndex);
								} else {
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								}
							}).catch(err => {
								uni.showToast({
									icon: 'none',
									title: err.msg || '操作失败'
								});
								console.error('Cancel Error:', err);
							});
						}
					}
				});
			},
			huodongquxiaos(item) {
				this.showCancel = false;
				this.$api.service.huodongquxiao({
					id: item.id
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					});
					this.page = 1;
					this.orderList = [];
					this.getList(this.currentIndex);
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '取消失败'
					});
					console.error('Cancel Error:', err);
				});
			},
			dingyue() {
				const allTmplIds = this.tmplIds;
				const requiredTmplId = 'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk';
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					return;
				}
				const otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);
				const shuffled = otherTmplIds.sort(() => 0.5 - Math.random());
				const selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						this.templateCategoryIds = [];
						selectedTmplIds.forEach((templId, index) => {
							if (res[templId] === 'accept') {
								const templateCategoryId = templateData[index].templateCategoryId;
								if (templateCategoryId === 10) {
									for (let i = 0; i < 15; i++) {
										this.templateCategoryIds.push(templateCategoryId);
									}
								} else {
									this.templateCategoryIds.push(templateCategoryId);
								}
							}
						});
					},
					fail: (err) => {}
				});
			},
			updateHighlight(options) {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.log('No userId, skipping updateHighlight');
					return;
				}
				this.$api.service.updataHighlight({
					userId: userId,
					role: 1,
					payType: options.tab
				}).then(res => {
					console.log('updateHighlight response:', res);
				}).catch(err => {
					console.error('updateHighlight error:', err);
				});
			},
			goevaluate(item) {
				uni.navigateTo({
					url: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}`
				});
			},
			applyT(item) {
				this.currentItem = item;
				this.showRefundModal = true;
			},
			confirmRefund() {
				this.showRefundModal = false;
				if (this.currentItem) {
					uni.navigateTo({
						url: `/user/tuicause?order_id=${this.currentItem.id}`
					});
				}
			},
			// New method to view refund details
			viewRefundDetails(item) {
				this.$api.service.refundProgress({
					id: item.id
				}).then(res => {
					if (res.code === "200" && res.data) {
						this.refundDetails = res.data;
						this.showRefundDetailsModal = true;
					} else {
						uni.showToast({
							icon: 'none',
							title: res.msg || '获取退款详情失败'
						});
					}
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: err.msg || '获取退款详情失败'
					});
					console.error('Refund details error:', err);
				});
			},
			// Helper to format refund status text
			refundStatusText(status) {
				switch (status) {
					case 1:
						return '审核中';
					case 2:
						return '已退款';
					case 3:
						return '驳回';
					default:
						return '未知状态';
				}
			},
			goChoose(item) {
				console.log(item)
				this.$store.commit('changeOrderInfo', item);
				uni.navigateTo({
					url: '/user/choose_master'
				});
			},
			confirmorder(item) {
				this.id = item.id;
				this.showConfirm = true;
			},
			confirmconfirm() {
				this.showConfirm = false;
				this.$api.service.confirmOrder({
					orderId: this.id
				}).then(res => {
					if (res.code === '-1') {
						uni.showToast({
							icon: 'none',
							title: res.msg,
							duration: 2000
						});
					} else {
						uni.showToast({
							icon: 'success',
							title: '操作成功'
						});
						uni.redirectTo({
							url: `/user/order_list?tab=7`
						});
					}
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '操作失败'
					});
					console.error('Confirm Error:', err);
				});
			},
			confirmCancel() {
				this.showCancel = false;
				this.$api.service.cancelOrder({
					id: this.id
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					});
					this.page = 1;
					this.orderList = [];
					this.getList(this.currentIndex);
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '取消失败'
					});
					console.error('Cancel Error:', err);
				});
			},
			cancelorder(item) {
				this.id = item.id;
				this.showCancel = true;
			},
			goUrl(e) {
				uni.navigateTo({
					url: e
				});
			},
			getList(nval) {
				const payType = nval !== undefined ? nval : this.currentIndex;
				console.log('getList called with payType:', payType, 'page:', this.page);

				// 显示加载状态
				uni.showLoading({
					title: '加载中...'
				});

				if (payType === 0) {
					return Promise.all([
						this.$api.service.huodongorder(),
						this.$api.service.userOrder({
							payType: 0,
							pageNum: this.page,
							pageSize: 10
						})
					]).then(([huodongRes, userOrderRes]) => {
						console.log('活动订单响应:', huodongRes);
						console.log('用户订单响应:', userOrderRes);

						const huodongList = (huodongRes.code === "200" && huodongRes.data) ?
							(Array.isArray(huodongRes.data) ? huodongRes.data.filter(item => item != null) : [
								huodongRes.data
							]) : [];
						this.huodonglist = huodongList;

						const userList = Array.isArray(userOrderRes.data.list) ? userOrderRes.data.list : [];
						const normalizedUserList = userList.map(item => ({
							...item,
							payType: parseInt(item.payType)
						}));

						const combinedList = [...huodongList, ...normalizedUserList];
						console.log('合并后的订单列表:', combinedList);

						// 使用 Vue.set 确保响应式更新
						this.$set(this, 'orderList', combinedList);
						this.status = userList.length < 10 ? 'nomore' : 'loadmore';

						console.log('订单数据加载成功，总数:', combinedList.length);
						console.log('当前 orderList 长度:', this.orderList.length);

						uni.hideLoading();

						// 强制触发视图更新
						this.$forceUpdate();

					}).catch(err => {
						uni.hideLoading();
						uni.showToast({
							icon: 'error',
							title: '获取订单失败'
						});
						console.error('API Error:', err);
						this.orderList = [];
						this.huodonglist = [];
						return Promise.reject(err);
					});
				} else {
					this.huodonglist = [];
					return this.$api.service.userOrder({
						payType,
						pageNum: this.page,
						pageSize: 10
					}).then(ress => {
						console.log('用户订单响应:', ress);
						let res = ress.data;
						const list = Array.isArray(res.list) ? res.list : [];
						const normalizedList = list.map(item => ({
							...item,
							payType: parseInt(item.payType)
						}));

						console.log('标准化后的订单列表:', normalizedList);

						// 使用 Vue.set 确保响应式更新
						this.$set(this, 'orderList', normalizedList);
						this.status = list.length < 10 ? 'nomore' : 'loadmore';

						console.log('订单数据加载成功，总数:', normalizedList.length);
						console.log('当前 orderList 长度:', this.orderList.length);

						uni.hideLoading();

						// 强制触发视图更新
						this.$forceUpdate();

					}).catch(err => {
						uni.hideLoading();
						uni.showToast({
							icon: 'error',
							title: '获取订单失败'
						});
						console.error('API Error:', err);
						this.orderList = [];
						return Promise.reject(err);
					});
				}
			},
			handleHeader(item) {
				this.currentIndex = item.value;
			},
			getcommissionRatio() {
				this.$api.service.commissionRatio().then(res => {
					this.jiaNum = res.data;
				}).catch(err => {
					console.error('getcommissionRatio Error:', err);
				});
			}
		},
		onLoad(options) {
			console.log('order_list onLoad, options:', options);
			if (options.from && options.from === 'tiaozhuan') {
				this.isFromTiaozhuan = true;
				console.log('来源是跳转页，返回时将执行默认返回');
			} else if (options.from && options.from === 'cart_play') {
				this.isFromCartPlay = true;
				console.log('来源是购物车下单页，返回时将跳转到订单页面');
			} else {
				this.isFromTiaozhuan = false;
				this.isFromCartPlay = false;
				console.log('来源是其他页面，返回时将跳转到"我的"页面');
			}

			// 初始化基础数据
			this.$api.service.remind().then(res => {
				if (res.code === "200") {
					this.reminddata = res.data.cancelRemind;
					this.paymentRemind = res.data.paymentRemind;
				}
			}).catch(err => {
				console.error('获取提醒信息失败:', err);
			});

			this.updateHighlight(options);
			this.currentIndex = options.tab ? parseInt(options.tab) : 0;
			this.page = 1; // 重置页码
			this.orderList = []; // 清空现有数据
			this.status = 'loadmore'; // 重置状态

			// 延迟加载数据，确保页面完全初始化
			this.$nextTick(() => {
				setTimeout(() => {
					console.log('开始加载订单数据...');
					this.getList(this.currentIndex).then(() => {
						console.log('订单数据加载完成，当前订单数量:', this.orderList.length);
					}).catch(err => {
						console.error('订单数据加载失败:', err);
					});
				}, 100);
			});

			this.getcommissionRatio();
		},
		onShow() {
			console.log('order_list onShow 触发');

			uni.$on('cancelOr', () => {
				this.currentIndex = 0;
				this.page = 1;
				this.getList();
			});

			// 获取当前页面信息
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const options = currentPage.options || {};

			console.log('order_list onShow, options:', options);
			console.log('当前订单数量:', this.orderList.length);

			// 如果是从 tiaozhuan 页面跳转过来且数据为空，强制刷新
			if (options.from === 'tiaozhuan' && this.orderList.length === 0) {
				console.log('从跳转页面过来且数据为空，强制刷新数据');
				this.page = 1;
				this.orderList = [];
				this.status = 'loadmore';
				this.getList(this.currentIndex).then(() => {
					console.log('强制刷新完成，订单数量:', this.orderList.length);
				});
				return;
			}

			// 检查是否是从其他页面跳转过来的（有特殊参数）
			if (options.tab !== undefined || options.refresh) {
				// 如果有tab参数或refresh参数，需要刷新数据
				if (options.tab !== undefined) {
					this.currentIndex = parseInt(options.tab);
				}
				this.page = 1;
				this.orderList = []; // 清空现有数据
				this.status = 'loadmore';
				this.getList(this.currentIndex);
			}
			// 如果没有特殊参数，说明是正常的页面显示，不需要重复加载数据
		},
		onBackPress() {
			if (this.isFromCartPlay) {
				// 如果是从购物车下单页面来的，返回到订单页面并刷新
				uni.redirectTo({
					url: '/user/order?refresh=1'
				});
			} else {
				// 其他情况返回到我的页面
				uni.redirectTo({
					url: '/pages/mine'
				});
			}
			return true;
		},
		onUnload() {
			if (this.isFromTiaozhuan) {
				uni.redirectTo({
					url: '/pages/mine'
				});
			} else if (this.isFromCartPlay) {
				// 如果是从购物车下单页面来的，页面卸载时跳转到订单页面并刷新
				uni.redirectTo({
					url: '/user/order?refresh=1'
				});
			}
		},
		watch: {
			currentIndex(nval) {
				this.page = 1;
				this.orderList = [];
				this.status = 'loadmore';
				this.getList(nval);
			}
		}
	};
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		min-height: 100vh;
		padding-top: 100rpx;

		.header {
			position: fixed;
			top: 0;
			width: 750rpx;
			height: 100rpx;
			background: #FFFFFF;
			display: flex;
			justify-content: space-around;
			align-items: center;
			z-index: 99;

			.header_item {
				max-width: 90rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #999999;
				display: flex;
				justify-content: center;
				flex-wrap: wrap;
				white-space: nowrap;

				.blue {
					margin-top: 8rpx;
					width: 38rpx;
					height: 6rpx;
					background: #2E80FE;
					border-radius: 4rpx;
				}
			}
		}

		.main {
			padding: 40rpx 30rpx;

			.main_item {
				width: 690rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				padding: 28rpx 36rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;

				.head {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;

					.no {
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;
						flex: 1;
						overflow: hidden;

						image {
							width: 120rpx;
							height: 120rpx;
							flex-shrink: 0;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.righ {
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						text-align: right;
						margin-left: 10rpx;
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.qzf,
					.qxdd,
					.lxsf,
					.qrwc,
					.qpl {
						width: 148rpx;
						height: 48rpx;
						background: #2E80FE;
						border-radius: 50rpx;
						font-size: 20rpx;
						font-weight: 500;
						line-height: 48rpx;
						text-align: center;
					}

					.qzf,
					.qrwc,
					.qpl {
						color: #fff;
					}

					.qxdd,
					.lxsf {
						background: #FFFFFF;
						border: 2rpx solid #2E80FE;
						color: #2E80FE;
					}
				}

				// 子订单样式
				.sub_orders {
					margin-top: 30rpx;
					padding-top: 20rpx;
					border-top: 1px solid #f0f0f0;

					.sub_title {
						font-size: 26rpx;
						font-weight: 500;
						color: #666;
						margin-bottom: 20rpx;
					}

					.sub_item {
						background: #f8f9fa;
						border-radius: 12rpx;
						padding: 20rpx;
						margin-bottom: 15rpx;

						.sub_head {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 15rpx;

							.sub_no {
								font-size: 22rpx;
								color: #666;
								max-width: 400rpx;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							.sub_status {
								font-size: 22rpx;
								color: #2E80FE;
								font-weight: 500;
							}
						}

						.sub_content {
							display: flex;
							justify-content: space-between;
							align-items: flex-end;

							.sub_info {
								flex: 1;

								.sub_amount {
									font-size: 24rpx;
									color: #333;
									font-weight: 500;
									margin-bottom: 8rpx;
								}

								.sub_reason {
									font-size: 22rpx;
									color: #666;
									margin-bottom: 8rpx;
									max-width: 300rpx;
									overflow: hidden;
									white-space: nowrap;
									text-overflow: ellipsis;
								}

								.sub_time {
									font-size: 20rpx;
									color: #999;
								}
							}

							.sub_actions {
								flex-shrink: 0;
								display: flex;
								gap: 10rpx;

								.sub_qzf {
									width: 120rpx;
									height: 40rpx;
									background: #2E80FE;
									border-radius: 40rpx;
									font-size: 18rpx;
									font-weight: 400;
									line-height: 40rpx;
									text-align: center;
									color: #fff;
									
									&.sub_reject {
										background: #ff6b6b;
									}
								}
							}
						}
					}
				}
			}

			.main_item_already {
				padding: 28rpx 36rpx;
				background-color: #fff;
				border-radius: 24rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;

				.qxdd {
					width: 148rpx;
					height: 48rpx;
					background: #FFFFFF;
					border-radius: 50rpx;
					font-size: 20rpx;
					font-weight: 500;
					line-height: 48rpx;
					text-align: center;
					border: 2rpx solid #2E80FE;
					color: #2E80FE;
				}

				.no {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;
						flex: 1;
						overflow: hidden;

						image {
							width: 120rpx;
							height: 120rpx;
							flex-shrink: 0;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
				}

				.shifu {
					margin-top: 20rpx;

					.shifu_item {
						display: inline-flex;
						flex-direction: column;
						align-items: center;
						margin-right: 20rpx;

						image {
							width: 90rpx;
							height: 90rpx;
							border-radius: 50%;
						}

						text {
							margin-top: 10rpx;
							font-size: 24rpx;
							color: #333333;
						}
					}
				}

				.tips {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #2E80FE;
				}

				.title {
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
				}

				.ok {
					font-size: 24rpx;
					color: #2E80FE;
					margin-top: 10rpx;
				}
			}
		}
	}

	.modal-content-red {
		color: #F60100;
		padding: 10rpx 30rpx;
		text-align: center;
	}

	.modal-content-details {
		padding: 20rpx;
		font-size: 28rpx;
		color: #333333;

		.detail-item {
			display: flex;
			margin-bottom: 10rpx;

			.label {
				font-weight: bold;
				width: 150rpx;
				flex-shrink: 0;
			}

			.value {
				flex-grow: 1;
			}
		}
	}
</style>
```